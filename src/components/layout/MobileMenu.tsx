"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

interface MobileMenuProps {
    isMobileMenu: boolean;
    handleMobileMenu: () => void;
}

export default function MobileMenu({ isMobileMenu, handleMobileMenu }: MobileMenuProps) {
    const [openSubMenus, setOpenSubMenus] = useState<{ [key: string]: boolean }>({});
    const pathname = usePathname();

    useEffect(() => {
        if (isMobileMenu) {
            handleMobileMenu();
        }
    }, [pathname]);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth > 991) {
                setOpenSubMenus({});
            }
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isMobileMenu) {
                handleMobileMenu();
            }
        };

        if (isMobileMenu) {
            document.addEventListener('keydown', handleKeyDown);
            // Prevent body scroll when menu is open
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [isMobileMenu, handleMobileMenu]);

    const handleToggleSubMenu = (key: string) => {
        setOpenSubMenus((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const isHashNav = (href: string) => href === "#";

    return (
        <>
            {/* Mobile Menu Overlay */}
            <div
                className={`mobile-menu-overlay ${isMobileMenu ? "active" : ""}`}
                onClick={handleMobileMenu}
                aria-hidden="true"
            />

            {/* Mobile Sidebar Menu */}
            <div className={`mobile-sidebar d-block d-lg-none ${isMobileMenu ? "mobile-menu-active" : ""}`}>
                {/* Logo Section */}
                <div className="logo-m">
                    <Link href="/" className="flex items-center justify-center" onClick={handleMobileMenu}>
                        <img
                            src="assets/img/logo/logo_full.svg"
                            alt="Motshwanelo IT Consulting"
                            className="h-12 w-auto filter brightness-0 invert transition-all duration-300"
                        />
                    </Link>
                </div>

                {/* Close Button */}
                <button
                    className="menu-close flex items-center justify-center w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 absolute top-6 right-6 z-10"
                    onClick={handleMobileMenu}
                    aria-label="Close mobile menu"
                >
                    <i className="fa-solid fa-xmark text-white text-xl" />
                </button>
                <div className="mobile-nav mt-8">
                    <ul className="space-y-2">
                        <li>
                            <Link
                                href="/"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-home text-lg"></i>
                                Home
                            </Link>
                        </li>
                        <li>
                            <Link
                                href="/about"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/about" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-users text-lg"></i>
                                About Us
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle flex items-center justify-between">
                                <Link
                                    href="/service"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 flex-1 ${pathname.startsWith("/service") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                    onClick={handleMobileMenu}
                                >
                                    <i className="fa-solid fa-cogs text-lg"></i>
                                    Services
                                </Link>
                                <button
                                    className={`submenu-button flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 mr-4 ${openSubMenus["service"] ? "bg-orange-500/20 text-orange-400 submenu-opened" : "text-white/70 hover:bg-white/10"}`}
                                    onClick={() => handleToggleSubMenu("service")}
                                    aria-label="Toggle services submenu"
                                    aria-expanded={openSubMenus["service"]}
                                >
                                    <i className={`fa-solid fa-chevron-down text-sm transition-transform duration-300 ${openSubMenus["service"] ? "rotate-180" : ""}`}></i>
                                </button>
                            </div>
                            <ul className={`sub-menu transition-all duration-300 overflow-hidden ${openSubMenus["service"] ? "max-h-96 opacity-100" : "max-h-0 opacity-0"}`}>
                                <li>
                                    <Link
                                        href="/service/data-centre"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/data-centre" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-server text-sm"></i>
                                        Data Centre Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/service/smart-city"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/smart-city" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-city text-sm"></i>
                                        Smart City Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/service/it-consulting"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/it-consulting" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-laptop-code text-sm"></i>
                                        IT Consulting
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/service/software-development"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/software-development" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-code text-sm"></i>
                                        Software Development
                                    </Link>
                                </li>
                            </ul>
                        </li>

                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle flex items-center justify-between">
                                <Link
                                    href="/projects"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 flex-1 ${pathname.startsWith("/projects") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                    onClick={handleMobileMenu}
                                >
                                    <i className="fa-solid fa-briefcase text-lg"></i>
                                    Projects
                                </Link>
                                <button
                                    className={`submenu-button flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 mr-4 ${openSubMenus["projects"] ? "bg-orange-500/20 text-orange-400 submenu-opened" : "text-white/70 hover:bg-white/10"}`}
                                    onClick={() => handleToggleSubMenu("projects")}
                                    aria-label="Toggle projects submenu"
                                    aria-expanded={openSubMenus["projects"]}
                                >
                                    <i className={`fa-solid fa-chevron-down text-sm transition-transform duration-300 ${openSubMenus["projects"] ? "rotate-180" : ""}`}></i>
                                </button>
                            </div>
                            <ul className={`sub-menu transition-all duration-300 overflow-hidden ${openSubMenus["projects"] ? "max-h-96 opacity-100" : "max-h-0 opacity-0"}`}>
                                <li>
                                    <Link
                                        href="/success-stories"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/success-stories" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-trophy text-sm"></i>
                                        Success Stories
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/milestones"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/milestones" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-flag-checkered text-sm"></i>
                                        Milestones
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <Link
                                href="/blog"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/blog") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-book text-lg"></i>
                                Blog
                            </Link>
                        </li>
                        <li>
                            <Link
                                href="/contact"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/contact" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-envelope text-lg"></i>
                                Contact
                            </Link>
                        </li>

                    </ul>

                    <div className="mobile-button mt-8 px-4">
                        <Link
                            className="flex items-center justify-center gap-3 w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                            href="/contact"
                            onClick={handleMobileMenu}
                        >
                            Start Digital Transformation
                            <i className="fa-solid fa-arrow-right text-sm" />
                        </Link>
                    </div>
                    <div className="single-footer-items mt-8 px-4">
                        <h3 className="text-white font-semibold text-lg mb-4 border-b border-white/20 pb-2">Contact Us</h3>
                        <div className="space-y-3">
                            <div className="contact-box flex items-center gap-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-300">
                                <div className="icon flex items-center justify-center w-10 h-10 rounded-full bg-blue-600/20">
                                    <i className="fa-solid fa-phone text-blue-400 text-sm"></i>
                                </div>
                                <div className="pera">
                                    <Link href="tel:+27123456789" className="text-white/90 hover:text-white transition-colors duration-300">
                                        +27 123 456 789
                                    </Link>
                                </div>
                            </div>
                            <div className="contact-box flex items-center gap-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-300">
                                <div className="icon flex items-center justify-center w-10 h-10 rounded-full bg-blue-600/20">
                                    <i className="fa-solid fa-envelope text-blue-400 text-sm"></i>
                                </div>
                                <div className="pera">
                                    <Link href="mailto:<EMAIL>" className="text-white/90 hover:text-white transition-colors duration-300">
                                        <EMAIL>
                                    </Link>
                                </div>
                            </div>
                            <div className="contact-box flex items-center gap-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-300">
                                <div className="icon flex items-center justify-center w-10 h-10 rounded-full bg-blue-600/20">
                                    <i className="fa-solid fa-map-marker-alt text-blue-400 text-sm"></i>
                                </div>
                                <div className="pera">
                                    <span className="text-white/90">
                                        Johannesburg, South Africa
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="contact-infos mt-8 px-4 pb-8">
                        <h3 className="text-white font-semibold text-lg mb-4 border-b border-white/20 pb-2">Follow Us</h3>
                        <ul className="social-icon flex items-center gap-3">
                            <li>
                                <Link
                                    href="#"
                                    className="flex items-center justify-center w-12 h-12 rounded-full bg-white/10 hover:bg-blue-600 text-white/80 hover:text-white transition-all duration-300 transform hover:scale-110"
                                >
                                    <i className="fa-brands fa-linkedin-in text-lg" />
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="#"
                                    className="flex items-center justify-center w-12 h-12 rounded-full bg-white/10 hover:bg-blue-600 text-white/80 hover:text-white transition-all duration-300 transform hover:scale-110"
                                >
                                    <i className="fa-brands fa-x-twitter text-lg" />
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="#"
                                    className="flex items-center justify-center w-12 h-12 rounded-full bg-white/10 hover:bg-red-600 text-white/80 hover:text-white transition-all duration-300 transform hover:scale-110"
                                >
                                    <i className="fa-brands fa-youtube text-lg" />
                                </Link>
                            </li>
                            <li>
                                <Link
                                    href="#"
                                    className="flex items-center justify-center w-12 h-12 rounded-full bg-white/10 hover:bg-pink-600 text-white/80 hover:text-white transition-all duration-300 transform hover:scale-110"
                                >
                                    <i className="fa-brands fa-instagram text-lg" />
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </>
    );
}
