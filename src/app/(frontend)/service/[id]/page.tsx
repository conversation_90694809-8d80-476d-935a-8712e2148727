import fs from 'fs';
import path from 'path';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import Layout from '@/components/layout/Layout';
import ServiceBanner from '@/components/sections/service-details/ServiceBanner';
import ServiceAbout from '@/components/sections/service-details/ServiceAbout';
import ServiceFeatures from '@/components/sections/service-details/ServiceFeatures';
import ServiceRequestForm from '@/components/sections/service-details/ServiceRequestForm';
import ServiceStats from '@/components/sections/service-details/ServiceStats';
import ServiceRelated from '@/components/sections/service-details/ServiceRelated';
import './service-enhanced.css';

export async function generateStaticParams() {
  const dir = path.join(process.cwd(), 'src/data/services');
  const files = fs.readdirSync(dir);
  return files.map((file) => ({ id: file.replace('.json', '') }));
}

async function getServiceData(id: string) {
  const filePath = path.join(process.cwd(), 'src/data/services', `${id}.json`);
  if (!fs.existsSync(filePath)) return null;
  const data = fs.readFileSync(filePath, 'utf-8');
  return JSON.parse(data);
}

async function getAllServices() {
  const dir = path.join(process.cwd(), 'src/data/services');
  const files = fs.readdirSync(dir).filter(f => f.endsWith('.json'));
  return files.map(file => {
    const id = file.replace('.json', '');
    const data = JSON.parse(fs.readFileSync(path.join(dir, file), 'utf-8'));
    return { id, ...data };
  });
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const data = await getServiceData(params.id);

  if (!data) {
    return {
      title: 'Service Not Found',
      description: 'The requested service could not be found.',
    };
  }

  return {
    title: `${data.pageBanner.pageTitle} | Motshwanelo IT Consulting`,
    description: data.pageBanner.description,
    keywords: [data.pageBanner.pageTitle, 'IT Services', 'Technology Solutions', 'Motshwanelo IT'],
    openGraph: {
      title: data.pageBanner.pageTitle,
      description: data.pageBanner.description,
      type: 'website',
      url: `https://motshwaneloitconsulting.co.za/service/${params.id}`,
      images: data.about?.image?.src ? [data.about.image.src] : [],
    },
  };
}

export default async function ServicePage({ params }: { params: { id: string } }) {
  const data = await getServiceData(params.id);
  const allServices = await getAllServices();

  if (!data) return notFound();

  // Get related services (exclude current service)
  const relatedServices = allServices.filter(service => service.id !== params.id).slice(0, 3);

  return (
    <Layout>
      <ServiceBanner banner={data.pageBanner} serviceId={params.id} />
      <ServiceAbout about={data.about} />
      <ServiceStats serviceId={params.id} />
      <ServiceFeatures features={data.features} />
      <ServiceRequestForm form={data.requestForm} />
      <ServiceRelated services={relatedServices} />
    </Layout>
  );
}
